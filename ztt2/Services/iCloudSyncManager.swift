//
//  iCloudSyncManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI
import Combine

/**
 * iCloud同步管理器
 * 负责管理iCloud同步功能的开启/关闭、权限检查、数据迁移等
 */
@MainActor
class iCloudSyncManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = iCloudSyncManager()
    
    // MARK: - Published Properties
    @Published var isSyncEnabled: Bool = false
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var isAvailable: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Constants
    private let syncEnabledKey = "icloud_sync_enabled"
    private let lastSyncDateKey = "last_sync_date"
    
    private init() {
        setupInitialState()
        observeSubscriptionChanges()
        setupSyncMonitoring()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查iCloud同步是否可用
     */
    func checkAvailability() async {
        do {
            // 检查CloudKit容器状态
            let container = CKContainer.default()
            let accountStatus = try await container.accountStatus()
            
            await MainActor.run {
                switch accountStatus {
                case .available:
                    self.isAvailable = true
                    self.errorMessage = nil
                    print("✅ iCloud账户可用")
                case .noAccount:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.no_account".localized
                    print("❌ 设备未登录iCloud账户")
                case .restricted:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.restricted".localized
                    print("❌ iCloud账户受限")
                case .couldNotDetermine:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.could_not_determine".localized
                    print("❌ 无法确定iCloud账户状态")
                case .temporarilyUnavailable:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.temporarily_unavailable".localized
                    print("❌ iCloud服务暂时不可用")
                @unknown default:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.unknown".localized
                    print("❌ 未知的iCloud账户状态")
                }
            }
        } catch {
            await MainActor.run {
                self.isAvailable = false
                self.errorMessage = error.localizedDescription
                print("❌ 检查iCloud可用性失败: \(error)")
            }
        }
    }
    
    /**
     * 检查用户是否有权限使用iCloud同步
     * 现在所有用户都可以使用iCloud同步功能
     */
    func hasPermission() -> Bool {
        // 确保用户已登录
        guard dataManager.currentUser != nil else {
            return false
        }

        // 所有用户都可以使用iCloud同步
        return true
    }
    
    /**
     * 尝试开启iCloud同步
     */
    func enableSync() async -> Bool {
        // 1. 检查权限
        guard hasPermission() else {
            await MainActor.run {
                self.errorMessage = "icloud_sync.error.permission_denied".localized
            }
            return false
        }
        
        // 2. 检查iCloud可用性
        await checkAvailability()
        guard isAvailable else {
            return false
        }
        
        // 3. 执行数据迁移（如果需要）
        let migrationSuccess = await performDataMigration()
        guard migrationSuccess else {
            return false
        }
        
        // 4. 启用同步
        await MainActor.run {
            self.isSyncEnabled = true
            self.syncStatus = .syncing
            self.userDefaults.set(true, forKey: self.syncEnabledKey)
            print("✅ iCloud同步已启用")
        }
        
        // 5. 执行首次同步
        await performSync()
        
        return true
    }
    
    /**
     * 关闭iCloud同步
     * 注意：由于现在统一使用CloudKit，此方法仅更新状态标志
     */
    func disableSync() {
        isSyncEnabled = false
        syncStatus = .idle
        userDefaults.set(false, forKey: syncEnabledKey)

        print("⏸️ iCloud同步状态已更新为关闭（数据仍会自动同步）")
    }
    
    /**
     * 手动触发同步
     */
    func triggerManualSync() async {
        guard isSyncEnabled && isAvailable else {
            errorMessage = "icloud_sync.error.not_available".localized
            return
        }
        
        await performSync()
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        isSyncEnabled = userDefaults.bool(forKey: syncEnabledKey)
        
        if let lastSyncTimestamp = userDefaults.object(forKey: lastSyncDateKey) as? Date {
            lastSyncDate = lastSyncTimestamp
        }
        
        // 异步检查可用性
        Task {
            await checkAvailability()
        }
    }
    
    /**
     * 监听订阅状态变化
     */
    private func observeSubscriptionChanges() {
        dataManager.$currentUser
            .sink { [weak self] user in
                guard let self = self else { return }

                // 检查订阅状态变化
                self.handleSubscriptionChange(user: user)
            }
            .store(in: &cancellables)
    }

    /**
     * 处理订阅状态变化
     */
    private func handleSubscriptionChange(user: User?) {
        guard let user = user,
              let subscription = user.subscription else {
            return
        }

        let currentType = subscription.subscriptionType
        let wasEnabled = isSyncEnabled

        // 如果从付费降级到免费用户
        if currentType == "free" && wasEnabled {
            Task {
                await handleSubscriptionDowngrade()
            }
        }
        // 如果从免费升级到付费用户
        else if currentType != "free" && !wasEnabled {
            handleSubscriptionUpgrade()
        }
    }

    /**
     * 处理订阅降级
     */
    private func handleSubscriptionDowngrade() async {
        print("⬇️ 检测到订阅降级，开始处理...")

        await MainActor.run {
            self.syncStatus = .migrating
        }

        do {
            // 1. 创建数据备份
            let backupSuccess = await createCloudDataBackup()
            guard backupSuccess else {
                throw DowngradeError.backupFailed
            }

            // 2. 关闭iCloud同步
            await MainActor.run {
                self.disableSync()
            }

            // 3. 确保本地数据完整性
            let verificationSuccess = await verifyLocalDataIntegrity()
            guard verificationSuccess else {
                throw DowngradeError.dataVerificationFailed
            }

            // 4. 显示降级通知
            await MainActor.run {
                self.showDowngradeNotification()
            }

            print("✅ 订阅降级处理完成")

        } catch {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = error.localizedDescription
                print("❌ 订阅降级处理失败: \(error)")
            }
        }
    }

    /**
     * 处理订阅升级
     */
    private func handleSubscriptionUpgrade() {
        print("⬆️ 检测到订阅升级，iCloud同步功能已可用")
        errorMessage = nil

        // 可以在这里显示升级成功的提示
        // 但不自动开启同步，让用户手动选择
    }

    /**
     * 设置同步状态监控
     */
    private func setupSyncMonitoring() {
        // 监听CloudKit通知
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleRemoteChange(notification)
            }
        }

        // 定期检查同步状态
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkSyncHealth()
            }
            .store(in: &cancellables)
    }

    /**
     * 处理远程数据变化
     */
    private func handleRemoteChange(_ notification: Notification) {
        guard isSyncEnabled else { return }

        print("📡 检测到远程数据变化")

        // 更新同步状态
        if syncStatus != .syncing {
            syncStatus = .syncing

            // 延迟更新为成功状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.syncStatus = .success
                self.lastSyncDate = Date()
                self.userDefaults.set(Date(), forKey: self.lastSyncDateKey)
            }
        }
    }

    /**
     * 检查同步健康状态
     */
    private func checkSyncHealth() {
        guard isSyncEnabled && isAvailable else { return }

        // 检查是否长时间未同步
        if let lastSync = lastSyncDate,
           Date().timeIntervalSince(lastSync) > 3600 { // 1小时
            print("⚠️ 长时间未同步，建议手动同步")
        }
    }
    
    /**
     * 执行数据迁移
     */
    private func performDataMigration() async -> Bool {
        // 如果已经在使用CloudKit，无需迁移
        if dataManager.persistenceController.isCloudKitEnabled {
            return true
        }

        await MainActor.run {
            self.syncStatus = .migrating
        }

        do {
            print("🔄 开始数据迁移...")

            // 1. 备份当前数据
            let backupSuccess = await backupLocalData()
            guard backupSuccess else {
                throw MigrationError.backupFailed
            }

            // 2. CloudKit存储模式已统一启用，无需切换
            print("☁️ CloudKit存储模式已统一启用")

            // 3. 等待数据迁移完成
            try await Task.sleep(nanoseconds: 1_000_000_000)

            // 4. 验证迁移结果
            let verificationSuccess = await verifyMigration()
            guard verificationSuccess else {
                throw MigrationError.verificationFailed
            }

            await MainActor.run {
                print("✅ 数据迁移完成")
            }

            return true
        } catch {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = "icloud_sync.error.migration_failed".localized
                print("❌ 数据迁移失败: \(error)")
            }
            return false
        }
    }

    /**
     * 备份本地数据
     */
    private func backupLocalData() async -> Bool {
        print("📦 备份本地数据...")

        // 这里实现数据备份逻辑
        // 在实际项目中，可以将重要数据导出到临时文件

        // 模拟备份过程
        try? await Task.sleep(nanoseconds: 500_000_000)

        print("✅ 本地数据备份完成")
        return true
    }

    /**
     * 验证迁移结果
     */
    private func verifyMigration() async -> Bool {
        print("🔍 验证迁移结果...")

        // 这里实现迁移验证逻辑
        // 检查关键数据是否正确迁移到CloudKit

        // 模拟验证过程
        try? await Task.sleep(nanoseconds: 500_000_000)

        print("✅ 迁移验证完成")
        return true
    }

    // MARK: - Downgrade Handling

    /**
     * 创建云端数据备份
     */
    private func createCloudDataBackup() async -> Bool {
        print("💾 创建云端数据备份...")

        do {
            // 1. 获取当前CloudKit数据
            let container = CKContainer.default()
            let database = container.privateCloudDatabase

            // 2. 查询所有记录类型
            let recordTypes = ["User", "Member", "PointRecord", "DiaryEntry", "GlobalRule", "MemberRule", "MemberPrize"]

            for recordType in recordTypes {
                let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))

                do {
                    let (matchResults, _) = try await database.records(matching: query)
                    let records = matchResults.compactMap { try? $0.1.get() }
                    print("📦 备份 \(recordType): \(records.count) 条记录")
                } catch {
                    print("⚠️ 备份 \(recordType) 失败: \(error)")
                }
            }

            // 3. 模拟备份过程
            try await Task.sleep(nanoseconds: 1_000_000_000)

            print("✅ 云端数据备份完成")
            return true

        } catch {
            print("❌ 创建云端数据备份失败: \(error)")
            return false
        }
    }

    /**
     * 验证本地数据完整性
     */
    private func verifyLocalDataIntegrity() async -> Bool {
        print("🔍 验证本地数据完整性...")

        await MainActor.run {
            let context = self.dataManager.persistenceController.container.viewContext

            do {
                // 检查关键数据是否存在
                let userRequest: NSFetchRequest<User> = User.fetchRequest()
                let users = try context.fetch(userRequest)

                let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
                let members = try context.fetch(memberRequest)

                print("✅ 本地数据验证完成 - 用户: \(users.count), 成员: \(members.count)")

            } catch {
                print("❌ 本地数据验证失败: \(error)")
            }
        }

        return true
    }

    /**
     * 显示降级通知
     */
    private func showDowngradeNotification() {
        // 发送通知给UI层显示降级提示
        NotificationCenter.default.post(
            name: .subscriptionDowngraded,
            object: nil,
            userInfo: [
                "message": "subscription_downgrade.notification.message".localized,
                "action": "subscription_downgrade.notification.action".localized
            ]
        )

        errorMessage = "icloud_sync.error.subscription_expired".localized
    }
    
    /**
     * 执行同步操作
     */
    private func performSync() async {
        await MainActor.run {
            self.syncStatus = .syncing
        }

        do {
            print("🔄 开始同步数据...")

            // 触发Core Data保存，这会自动启动CloudKit同步
            await MainActor.run {
                self.dataManager.persistenceController.save()
            }

            // 等待同步完成（模拟）
            try await Task.sleep(nanoseconds: 1_000_000_000)

            await MainActor.run {
                self.syncStatus = .success
                self.lastSyncDate = Date()
                self.userDefaults.set(Date(), forKey: self.lastSyncDateKey)
                self.errorMessage = nil
                print("✅ 数据同步完成")
            }
        } catch {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = error.localizedDescription
                print("❌ 数据同步失败: \(error)")
            }
        }
    }

    // MARK: - 同步诊断工具

    /**
     * 诊断数据同步问题
     */
    func diagnoseDiarySyncIssues() async -> [String] {
        var issues: [String] = []

        print("🔍 开始诊断数据同步问题（成长日记和AI分析报告）...")

        // 1. 检查CloudKit配置
        if !isSyncEnabled {
            issues.append("❌ CloudKit同步未启用")
        } else {
            issues.append("✅ CloudKit同步已启用")
        }

        // 2. 检查网络连接
        // 这里可以添加网络检查逻辑
        issues.append("ℹ️ 网络连接检查需要实际网络测试")

        // 3. 检查本地数据
        await MainActor.run {
            let context = self.dataManager.persistenceController.container.viewContext

            // 检查DiaryEntry数据
            let diaryRequest: NSFetchRequest<DiaryEntry> = DiaryEntry.fetchRequest()
            do {
                let diaryEntries = try context.fetch(diaryRequest)
                issues.append("📊 本地成长日记数量: \(diaryEntries.count)")

                // 检查最近的日记条目
                if let latestDiary = diaryEntries.sorted(by: { $0.createdAt ?? Date.distantPast > $1.createdAt ?? Date.distantPast }).first {
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    formatter.timeStyle = .short
                    issues.append("📝 最新日记创建时间: \(formatter.string(from: latestDiary.createdAt ?? Date()))")
                    issues.append("👤 最新日记关联成员: \(latestDiary.member?.name ?? "未知")")
                }
            } catch {
                issues.append("❌ 无法获取本地日记数据: \(error.localizedDescription)")
            }

            // 检查AIReport数据
            let aiReportRequest: NSFetchRequest<AIReport> = AIReport.fetchRequest()
            do {
                let aiReports = try context.fetch(aiReportRequest)
                issues.append("🤖 本地AI分析报告数量: \(aiReports.count)")

                // 检查最近的AI报告
                if let latestReport = aiReports.sorted(by: { $0.createdAt ?? Date.distantPast > $1.createdAt ?? Date.distantPast }).first {
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    formatter.timeStyle = .short
                    issues.append("📊 最新AI报告创建时间: \(formatter.string(from: latestReport.createdAt ?? Date()))")
                    issues.append("👤 最新AI报告关联成员: \(latestReport.member?.name ?? "未知")")
                    issues.append("📋 最新AI报告类型: \(latestReport.reportType ?? "未知")")
                }
            } catch {
                issues.append("❌ 无法获取本地AI报告数据: \(error.localizedDescription)")
            }

            // 检查Member数据
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            do {
                let members = try context.fetch(memberRequest)
                issues.append("👥 本地成员数量: \(members.count)")

                for member in members {
                    let diaryCount = member.diaryEntries?.count ?? 0
                    issues.append("👤 \(member.name ?? "未知成员")的日记数量: \(diaryCount)")
                }
            } catch {
                issues.append("❌ 无法获取本地成员数据: \(error.localizedDescription)")
            }
        }

        // 4. 检查CloudKit记录
        await checkCloudKitRecords(issues: &issues)

        print("🔍 诊断完成，发现 \(issues.count) 个检查项")
        return issues
    }

    /**
     * 检查CloudKit记录
     */
    private func checkCloudKitRecords(issues: inout [String]) async {
        do {
            let container = CKContainer.default()
            let database = container.privateCloudDatabase

            // 检查DiaryEntry记录
            let diaryQuery = CKQuery(recordType: "DiaryEntry", predicate: NSPredicate(value: true))
            let (diaryResults, _) = try await database.records(matching: diaryQuery)
            let diaryRecords = diaryResults.compactMap { try? $0.1.get() }
            issues.append("☁️ CloudKit中的日记记录数量: \(diaryRecords.count)")

            // 检查AIReport记录
            let aiReportQuery = CKQuery(recordType: "AIReport", predicate: NSPredicate(value: true))
            let (aiReportResults, _) = try await database.records(matching: aiReportQuery)
            let aiReportRecords = aiReportResults.compactMap { try? $0.1.get() }
            issues.append("☁️ CloudKit中的AI报告记录数量: \(aiReportRecords.count)")

            // 检查Member记录
            let memberQuery = CKQuery(recordType: "Member", predicate: NSPredicate(value: true))
            let (memberResults, _) = try await database.records(matching: memberQuery)
            let memberRecords = memberResults.compactMap { try? $0.1.get() }
            issues.append("☁️ CloudKit中的成员记录数量: \(memberRecords.count)")

            // 检查最近的记录
            if let latestDiaryRecord = diaryRecords.sorted(by: {
                ($0["createdAt"] as? Date ?? Date.distantPast) > ($1["createdAt"] as? Date ?? Date.distantPast)
            }).first {
                let createdAt = latestDiaryRecord["createdAt"] as? Date ?? Date()
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.timeStyle = .short
                issues.append("☁️ CloudKit最新日记时间: \(formatter.string(from: createdAt))")
            }

        } catch {
            issues.append("❌ 无法访问CloudKit: \(error.localizedDescription)")

            if let ckError = error as? CKError {
                switch ckError.code {
                case .notAuthenticated:
                    issues.append("⚠️ 未登录iCloud账户")
                case .networkUnavailable:
                    issues.append("⚠️ 网络不可用")
                case .quotaExceeded:
                    issues.append("⚠️ iCloud存储空间不足")
                default:
                    issues.append("⚠️ CloudKit错误: \(ckError.localizedDescription)")
                }
            }
        }
    }

    /**
     * 强制刷新本地数据
     */
    func forceRefreshLocalData() async {
        await MainActor.run {
            print("🔄 强制刷新本地数据...")
            let context = self.dataManager.persistenceController.container.viewContext
            context.refreshAllObjects()

            // 发送同步完成通知，触发UI更新
            NotificationCenter.default.post(
                name: NSNotification.Name("CloudKitSyncCompleted"),
                object: nil
            )
            print("✅ 本地数据刷新完成")
        }
    }
}

// MARK: - Sync Status Enum

enum SyncStatus {
    case idle
    case syncing
    case migrating
    case success
    case failed

    var displayText: String {
        switch self {
        case .idle:
            return "icloud_sync.status.idle".localized
        case .syncing:
            return "icloud_sync.status.syncing".localized
        case .migrating:
            return "icloud_sync.status.migrating".localized
        case .success:
            return "icloud_sync.status.success".localized
        case .failed:
            return "icloud_sync.status.failed".localized
        }
    }

    var iconName: String {
        switch self {
        case .idle:
            return "icloud"
        case .syncing, .migrating:
            return "icloud.and.arrow.up"
        case .success:
            return "icloud.and.arrow.up.fill"
        case .failed:
            return "icloud.slash"
        }
    }
}

// MARK: - Migration Error Enum

enum MigrationError: LocalizedError {
    case backupFailed
    case verificationFailed
    case dataCorrupted

    var errorDescription: String? {
        switch self {
        case .backupFailed:
            return "数据备份失败"
        case .verificationFailed:
            return "迁移验证失败"
        case .dataCorrupted:
            return "数据损坏"
        }
    }
}

// MARK: - Downgrade Error Enum

enum DowngradeError: LocalizedError {
    case backupFailed
    case dataVerificationFailed
    case syncDisableFailed

    var errorDescription: String? {
        switch self {
        case .backupFailed:
            return "subscription_downgrade.error.backup_failed".localized
        case .dataVerificationFailed:
            return "subscription_downgrade.error.verification_failed".localized
        case .syncDisableFailed:
            return "subscription_downgrade.error.disable_failed".localized
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let subscriptionDowngraded = Notification.Name("subscriptionDowngraded")
    static let subscriptionUpgraded = Notification.Name("subscriptionUpgraded")
}

// MARK: - Reset Methods Extension

extension iCloudSyncManager {

    /**
     * 重置同步状态
     * 用于清除所有数据后的状态重置
     */
    func resetSyncState() {
        print("🔄 重置iCloud同步状态...")

        // 重置发布的属性
        isSyncEnabled = false
        syncStatus = .idle
        lastSyncDate = nil
        isAvailable = false
        errorMessage = nil

        // 清除UserDefaults中的同步相关设置
        userDefaults.removeObject(forKey: syncEnabledKey)
        userDefaults.removeObject(forKey: lastSyncDateKey)

        // 取消所有观察者
        cancellables.removeAll()

        // 异步重新检查可用性
        Task {
            await checkAvailability()
        }

        print("✅ iCloud同步状态重置完成")
    }
}
